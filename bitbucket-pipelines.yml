image: node:18

definitions:
  caches:
    npm: ~/.npm

pipelines:
  branches:
    master:
      - step:
          name: Deploy to Production
          caches:
            - npm
          script:
            - npm ci
            - cp .env.prod .env
            - npx serverless deploy --stage prod --verbose
          services:
            - docker
    development:
      - step:
          name: Deploy to Development
          caches:
            - npm
          script:
            - npm ci
            - cp .env.dev .env
            - sudo sls deploy --stage dev --verbose
          services:
            - docker
