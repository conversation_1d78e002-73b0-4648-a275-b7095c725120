const axios = require('axios'); // Importing Axios for making HTTP requests
const dotenv = require('dotenv'); // Importing dotenv for environment variables
dotenv.config(); // Loading environment variables from .env file

// Function to send Pusher notification for a new order
module.exports.sendPusherNotification = async (orderData, source = "web", event = "new-order") => {
    try {

        const orderId = (source == "web") ? orderData.order_id : orderData.r_id + '-' + orderData.bid;
        const eventName = (event == "new-order") ? event : "bulk-order";
        // Creating the payload for the Pusher notification
        let data = JSON.stringify({
            "business_id": orderData.r_id,
            "event": eventName,
            "data": {
                "order_id": orderId,
                "business_id": orderData.r_id,
                "branch_id": orderData.bid,
                "message": "Received a new order"
            }
        });
        console.log(data);
        // Configuring the Axios request
        let config = {
            method: 'post', // HTTP method (POST)
            maxBodyLength: Infinity, // Maximum body length
            url: process.env.PUSHER_BASE_URL, // Pusher base URL from environment variables
            headers: {
                'Content-Type': 'application/json' // Request content type
            },
            data: data // Request payload
        };

        // Sending the request to Pusher
        const response = await axios.request(config);
        // console.log(response);

        return response;
    } catch (error) {
        // Logging any errors that occur during the try block
        console.error('Error in sendPusherNotification:', error.message);
        // Returning the error
        return error;
    }
}

// Function to send Pusher notification for a new order
module.exports.sendFcmNotification = async (orderData, businessSettings) => {

    const businessId = orderData.r_id;
    const branchId = orderData.bid;
    const orderId = orderData.order_id;
    const businessName = businessSettings.contact_name;

    const data = {
        to: `/topics/${businessId}-${branchId}`,
        collapse_key: 'type_a',
        notification: {
            body: "New Order Recieved #"+orderId,
            title: businessName,
            sound: 'default',
        },
        data: {
            click_action: 'FLUTTER_NOTIFICATION_CLICK',
            order_id: orderId
        }
    };

    const config = {
        headers: {
            'Content-Type': 'application/json',
            'Authorization': `key=${process.env.PARTNER_APP_FCM_KEY}` // FCM server key should be stored in environment variables
        }
    };

    try {
        const response = await axios.post('https://fcm.googleapis.com/fcm/send', data, config);
        console.log('Successfully sent message:', response.data);
        return {
            statusCode: 200,
            body: JSON.stringify({ message: 'Notification sent successfully' }),
        };

    } catch (error) {
        console.log('Error sending message:', error.response ? error.response.data : error.message);
        return {
            statusCode: 500,
            body: JSON.stringify({ message: 'Error sending notification' }),
        };
    }
}