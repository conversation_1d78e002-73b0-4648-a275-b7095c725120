const integrationModel = require('../../models/integrationModel');
const client = require("../client")


module.exports.chargeBankPayment = async (businessId, paymentDetails) => {

    try {

        const fetchIntegrationData = await integrationModel.getIntegrationDetails(businessId, 'payment', 42, true);

        const { username: BflhAccountId, password: BflhAccountPassword, url: BflhAccountSession, key: BflhAccountKey } = fetchIntegrationData;

        const paymentRequest = JSON.stringify({
            "apiOperation": "INITIATE_CHECKOUT",
            "interaction": {
                "merchant": {
                    "name": BflhAccountId
                },
                "operation": "PURCHASE",
                "returnUrl": paymentDetails.return_url
            },
            "order": {
                "id": paymentDetails.order_id,
                "amount": paymentDetails.amount,
                "currency": "PKR",
                "description": "INV-" + paymentDetails.order_id
            }
        })
        const headers = {
            'authorization': 'Basic ' + BflhAccountKey,
            'cache-control': "no-cache",
            'content-type': "application/json"
        }
        const sessionURL = BflhAccountSession + BflhAccountId + "/session"
        const request = await client.post(sessionURL, paymentRequest, { headers: headers });

        return { status: 200, response: request.data }
    } catch (error) {
        // Handle errors
        console.error(error);
        return { status: 400, response: error }; // Rethrow the error to be handled by the caller
    }

}