const ordersHelper = require('../../uitls/orders/ordersHelper');
const integrationModel = require('../../models/integrationModel');
const client = require("../client")
const FormData = require("form-data")

const dotenv = require('dotenv'); // Importing dotenv for environment variables
dotenv.config(); // Loading environment variables from .env file


module.exports.chargePaymobPayment = async (businessId, paymentDetails) => {

    let status = 400;
    let orderStatus = processPayment = response = {};
    try {
        let orderId = paymentDetails.order_id;
        const fetchOrderData = await ordersHelper.getOrderDetails(businessId, orderId);


        const fetchIntegrationData = await integrationModel.getIntegrationDetails(businessId, 'payment', 41, true);

        let mapOrderItemsForRequest = await mapOrderDetails(fetchOrderData);

        const apiKey = fetchIntegrationData.key;
        const accountId = fetchIntegrationData.username;
        const settings = JSON.parse(fetchIntegrationData.settings);
        mapOrderItemsForRequest.integration_id = settings.integration_id;
        if (!apiKey) {
            response = {
                status: "404",
                message: "Unauthorized - API key missing",
                result: "",
            };
        }
        const postData = { api_key: apiKey };
        const { statusCode, result } = await getAuthToken(postData);
        if (statusCode === 200) {
            let orderJson = { ...mapOrderItemsForRequest, auth_token: result.token, merchant_id: result.profile.id };
            orderStatus = await orderCreate(orderJson);

            orderJson.order_id = orderStatus.result.id;
            orderJson.integration_id = settings.integration_id;
            if (orderStatus.statusCode === 200) {
                const processPayment = await orderPayment(orderJson);

                let paymentResponseResult = processPayment.result;
                paymentResponseResult.account_id = accountId;
                response = {
                    status: processPayment.statusCode,
                    message: processPayment.message,
                    result: paymentResponseResult,
                };
                return response;
            } else {
                response = {
                    status: orderStatus.statusCode,
                    message: orderStatus.message,
                    result: orderStatus.result,
                };
            }
        } else {
            response = { status, message: message, response: result.result };
        }
        return response;

    } catch (error) {
        console.error("Error:", error.response ? error.response.data : error.message);
        response = { status, message: error.response ? error.response.data : error.message };
    }
    return response;
}

module.exports.chargePaymentStatus = async (paymentDetails) => {

    let status = 400;
    let orderStatus = processPayment = response = {};
    try {
        const paymentResponse = paymentDetails.obj;
        const orderDetails = paymentResponse.order;

        if (paymentResponse.success == true && orderDetails.merchant_order_id != "") {
            const paidAmount = orderDetails.paid_amount_cents / 100;
            const orderData = orderDetails.data;

            const postData = {
                order_id : orderData.order_id,
                business_id : orderData.business_id,
                branch_id : orderData.branch_id ? orderData.branch_id : 0,
                source : orderData.source,
                amount : paidAmount,
            }
            const { statusCode, result } = await orderStatusUpdate(postData);

        }
        return response;

    } catch (error) {
        console.error("Error:", error.response ? error.response.data : error.message);
        response = { status, message: error.response ? error.response.data : error.message };
    }
    return response;
}
// Function to map order details
const mapOrderDetails = async (orderDetails) => {

    const currentTimestampInSeconds = Math.floor(Date.now() / 1000);
    let orderData = {
        delivery_needed: "true",
        amount_cents: +orderDetails.gtotal * 100,
        currency: "PKR",
        merchant_order_id: orderDetails.orderid + "-" + currentTimestampInSeconds,
        data: {
            order_id: orderDetails.orderid,
            branch_id: orderDetails.bid,
            business_id: orderDetails.r_id,
            source: orderDetails.source
        },
        shipping_data: {
            apartment: "None",
            address: orderDetails.address,
            email: orderDetails.email,
            floor: orderDetails.address,
            first_name: orderDetails.name,
            street: orderDetails.address,
            building: orderDetails.address,
            phone_number: orderDetails.mobile_phone,
            postal_code: orderDetails.postal_code ? orderDetails.postal_code : "54000",
            city: orderDetails.city,
            country: "Pakistan",
            last_name: orderDetails.name,
            state: orderDetails.area,
        },
        shipping_details: {
            note: orderDetails.note,
            number_of_packages: 1,
            weight: 0,
            weight_unit: "Kilogram"
        },
        billing_data: {
            apartment: "None",
            email: orderDetails.email,
            floor: orderDetails.address,
            first_name: orderDetails.name,
            street: orderDetails.address,
            building: orderDetails.address,
            phone_number: orderDetails.mobile_phone,
            postal_code: orderDetails.postal_code ? orderDetails.postal_code : "54000",
            city: orderDetails.city,
            country: "Pakistan",
            last_name: orderDetails.name,
            state: orderDetails.area,
        }
    };

    orderData.items = await mapOrderItemDetails(orderDetails)

    return orderData;
}

// Function to execute a database query
const mapOrderItemDetails = async (orderDetails) => {

    const orderItems = orderDetails.order_details;
    const mappedItems = orderItems.map(item => ({
        name: item.dname,
        quantity: item.dqty,
        amount_cents: item.dtotal,
        description: "",
    }));

    return mappedItems;
}

const getAuthToken = async (postData) => {
    let status = 400;
    let message = "";
    let result = {};
    try {
        await client
            .post("https://pakistan.paymob.com/api/auth/tokens", postData)
            .then((response) => {
                status = 200;
                message = "Authorized";
                result = response.data;
            })
            .catch((error) => {
                console.error("Error:", error.message);
                status = 404;
                message = "Authorization Failed";
            });
    } catch (error) {
        console.error(error.message);
        message = error.message
    }
    return { statusCode: status, message: message, result: result };
};

const orderCreate = async (postData) => {
    let status = 400;
    let message = "";
    let result = {};
    try {
        await client
            .post("https://pakistan.paymob.com/api/ecommerce/orders", postData)
            .then((response) => {
                status = 200;
                message = "Order registered successfully";
                result = response.data;
            })
            .catch((error) => {
                status = 404;
                message = error.data;
                console.error("Error: Order", error.data);
            });
    } catch (err) {
        console.error("Error Order:", err.message);
        message = err.message;
    }
    return { statusCode: status, message: message, result: result };
};

const orderPayment = async (postData) => {
    let status = 400;
    let result = {};
    try {
        await client
            .post("https://pakistan.paymob.com/api/acceptance/payment_keys", postData)
            .then((response) => {
                status = 200;
                message = "Payment proceeded successfully";
                result = response.data;
                // console.error("Payment keys:", result);
            })
            .catch((error) => {
                status = 404;
                message = error.response ? error.response.data : error.message
                console.error("Error Payment keys:", error.response.data ? error.response.data : error.message);
            });
    } catch (error) {
        status = 401;
        console.error("Error Payment keys:", error.response.data ? error.response.data : error.message);
        message = error.response ? error.response.data : error.message;
    }
    return { statusCode: status, message: message, result: result };
};

const orderStatusUpdate = async (postData) => {
    let status = 400;
    let result = {};
    let data = new FormData();
    try {
        data.append('order_id', postData.order_id);
        data.append('eatout_id', postData.business_id);
        data.append('eatout_uid', '633256');
        data.append('bid', postData.branch_id);
        data.append('status', 'Confirmed');
        data.append('source', postData.source);
        data.append('payment_type', "1");
        data.append('comment', '');

        await client
            .post(process.env.API_PATH+"order_status_update", data)
            .then((response) => {

                status = 200;
                message = "";
                result = response.data;
            })
            .catch((error) => {
                status = 404;
                message = error.response ? error.response.data : error.message
                console.error("Error Payment keys:", error.response.data ? error.response.data : error.message);
            });
    } catch (error) {
        status = 401;
        console.error("Error Payment keys:", error.response.data ? error.response.data : error.message);
        message = error.response ? error.response.data : error.message;
    }
    return { statusCode: status, message: message, result: result };
};