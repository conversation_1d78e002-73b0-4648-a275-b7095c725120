const integrationModel = require("../../models/integrationModel");
const jwtAuthenticator = require("../../uitls/jwtAuthenticator");
const client = require("../client");
const dotenv = require("dotenv");
dotenv.config();
const stripe = require("stripe")(process.env.STRIPE_KEY);

module.exports.registerCustomer = async (
  businessId,
  paymentDetails,
  requestHeaders
) => {

  try {
    const { token } = paymentDetails;
    const headers = requestHeaders;
    let cardId, customerCardId, addCard, cardsList;
    const fetchIntegrationData = await integrationModel.getIntegrationDetails(
      businessId,
      "payment",
      8,
      true
    );

    // Check if integration data is found
    if (fetchIntegrationData) {
      // Extract Stripe business account details
      let stripeBusinessAccountId = fetchIntegrationData.username;
      const stripeBusinessAccountSettings =
        JSON.parse(fetchIntegrationData.settings) || {};

      // Calculate charges and extract order details
      const charges = paymentDetails.amount * 100 || 0;
      const orderId = paymentDetails.order_id;
      const customerId = paymentDetails.customer_id;
      const saveCard = paymentDetails.save_card || false;

      if (customerId && headers.Authorization) {
        const tokenStatus = await jwtAuthenticator.verifyToken(
          headers.Authorization
        );
        if (tokenStatus.valid == false) {
          return { status: 401, message: tokenStatus.message, response: {} };
        }
      } else if (customerId && !headers.Authorization) {
        return {
          status: 401,
          message: "UnAuthorized: Access token missing",
          response: {},
        };
      }
      // Initialize customerExist object
      let customerExist = { status: 404 };

      // Retrieve branch-wise account for Stripe
      stripeBusinessAccountId = await getBranchWiseAccount(
        stripeBusinessAccountSettings,
        stripeBusinessAccountId,
        0
      );

      // Prepare payment request object
      let paymentRequest = {
        amount: +parseInt(charges),
        currency: "cad",
        description: "INV-" + orderId,
        capture: true,
        application_fee_amount: 0,
        metadata: {
          order_id: orderId,
          business_id: businessId,
          preauth: false,
          source: paymentDetails.source,
        },
      };
      // Check if customer ID is provided
      if (customerId) {
        // Check if customer exists in Stripe
        customerExist = await checkCustomerExists(
          customerId,
          stripeBusinessAccountId
        );
      }

      // Add customer with card if saveCard is true and customer doesn't exist
      if (saveCard && customerExist.status == 404) {
        customerExist = await addCustomerWithCard(
          saveCard,
          customerExist,
          paymentDetails,
          stripeBusinessAccountId
        );
        if (customerExist.status != 200) {
          return { status: 400, message: customerExist.message, response: {} };
        }
        cardsList = await module.exports.getCustomerCardDetails(
          businessId,
          customerExist.response.id,
          paymentDetails
        );
        if (cardsList.status === 200 && Array.isArray(cardsList.response) && cardsList.response.length > 0) {
          cardId = cardsList.response[0].id; // Access the id from the first object in the response array
          console.log('Card ID:', cardId);
        } else {
          console.log('Failed to add card or no cards available:', cardsList.message);
        }
        console.log("businessId: " + businessId);
        console.log("paymentDetails.user_id " + paymentDetails.user_id);
        console.log("paymentDetails.email " + paymentDetails.email);
        console.log("customerExist.response.id " + customerExist.response.id);
        console.log("CardId " + cardId);
        const saveCustomerAccount = await createCustomer(
          businessId,
          paymentDetails.user_id,
          paymentDetails.email,
          customerExist.response.id
        );
        customerCardId = cardId;
        paymentRequest.customer = customerExist.response.id;
        customerExist.status = 200;
      } else if (
        customerExist.status == 200 &&
        customerExist.response.id == customerId &&
        token && saveCard
      ) {
        // Use existing customer and token if both are provided
        paymentRequest.customer = customerId;
        addCard = await module.exports.addCustomerCardDetails(
          businessId,
          customerId,
          paymentDetails
        );
        if (addCard.status === 200) {
            cardsList = await module.exports.getCustomerCardDetails(
            businessId,
            customerId,
            paymentDetails
          );
          if (cardsList.status === 200 && Array.isArray(cardsList.response) && cardsList.response.length > 0) {
            cardId = cardsList.response[0].id; // Access the id from the first object in the response array
            console.log('Card ID:', cardId);
          } else {
            console.log('Failed to add card or no cards available:', cardsList.message);
          }
          customerCardId = cardId;
        } else {
          console.log('Failed to add card or no cards available:', addCard.message);
        }
      } else {
        // Use only token if no customer exists or token is provided
        paymentRequest.source = token;
      }

      // Return success response
      return {
        status: 200,
        message: "Success",
        response: { customer_id: paymentRequest.customer, card_id: customerCardId },
      };
    }

    // Return error response if no integration data is found
    return { status: 404, message: "No Integrations found", response: {} };
  } catch (error) {
    // Handle errors
    console.error(error);
    return { status: 400, response: error }; // Rethrow the error to be handled by the caller
  }
};

module.exports.chargePayment = async (
  businessId,
  paymentDetails,
  requestHeaders
) => {
  try {
    const { token } = paymentDetails;
    const headers = requestHeaders;
    const fetchIntegrationData = await integrationModel.getIntegrationDetails(
      businessId,
      "payment",
      8,
      true
    );

    // Check if integration data is found
    if (fetchIntegrationData) {
      // Extract Stripe business account details
      let stripeBusinessAccountId = fetchIntegrationData.username;
      const stripeBusinessAccountSettings =
        JSON.parse(fetchIntegrationData.settings) || {};

      // Calculate charges and extract order details
      const charges = paymentDetails.amount * 100 || 0;
      const orderId = paymentDetails.order_id;
      const customerId = paymentDetails.customer_id;
      const saveCard = paymentDetails.save_card || false;

      if (customerId && headers.Authorization) {
        const tokenStatus = await jwtAuthenticator.verifyToken(
          headers.Authorization
        );
        if (tokenStatus.valid == false) {
          return { status: 401, message: tokenStatus.message, response: {} };
        }
      } else if (customerId && !headers.Authorization) {
        return {
          status: 401,
          message: "UnAuthorized: Access token missing",
          response: {},
        };
      }
      // Initialize customerExist object
      let customerExist = { status: 404 };

      // Retrieve branch-wise account for Stripe
      stripeBusinessAccountId = await getBranchWiseAccount(
        stripeBusinessAccountSettings,
        stripeBusinessAccountId,
        0
      );

      // Prepare payment request object
      let paymentRequest = {
        amount: +parseInt(charges),
        currency: "cad",
        description: "INV-" + orderId,
        capture: true,
        application_fee_amount: 0,
        metadata: {
          order_id: orderId,
          business_id: businessId,
          preauth: false,
          source: paymentDetails.source,
        },
      };
      // Check if customer ID is provided
      if (customerId) {
        // Check if customer exists in Stripe
        customerExist = await checkCustomerExists(
          customerId,
          stripeBusinessAccountId
        );
      }

      // Add customer with card if saveCard is true and customer doesn't exist
      if (saveCard && customerExist.status == 404) {
        customerExist = await addCustomerWithCard(
          saveCard,
          customerExist,
          paymentDetails,
          stripeBusinessAccountId
        );
        if (customerExist.status != 200) {
          return { status: 400, message: customerExist.message, response: {} };
        }
        const saveCustomerAccount = await createCustomer(
          businessId,
          paymentDetails.user_id,
          paymentDetails.email,
          customerExist.response.id
        );
        paymentRequest.customer = customerExist.response.id;
        customerExist.status = 200;
      } else if (
        customerExist.status == 200 &&
        customerExist.response.id == customerId &&
        token
      ) {
        // Use existing customer and token if both are provided
        paymentRequest.customer = customerId;
        paymentRequest.source = token;
      } else {
        // Use only token if no customer exists or token is provided
        paymentRequest.source = token;
      }

      // Create charge request
      let chargeRequest = await stripe.charges.create(paymentRequest, {
        stripeAccount: stripeBusinessAccountId,
      });
      // Add customer ID to chargeRequest object
      chargeRequest.customer_id =
        customerExist.status == 200 ? customerExist.response.id : "" || "";

      // Return success response
      return { status: 200, message: "Success", response: chargeRequest };
    }

    // Return error response if no integration data is found
    return { status: 404, message: "No Integrations found", response: {} };
  } catch (error) {
    // Handle errors
    console.error(error);
    return { status: 400, response: error }; // Rethrow the error to be handled by the caller
  }
};

module.exports.getCustomerCardDetails = async (
  businessId,
  customerId,
  paymentDetails
) => {
  try {
    const fetchIntegrationData = await integrationModel.getIntegrationDetails(
      businessId,
      "payment",
      8,
      true
    );
    if (fetchIntegrationData) {
      let stripeBusinessAccountId = fetchIntegrationData.username;
      const stripeBusinessAccountSettings = JSON.parse(
        fetchIntegrationData.settings
      );

      stripeBusinessAccountId = await getBranchWiseAccount(
        stripeBusinessAccountSettings,
        stripeBusinessAccountId,
        0
      );

      if (customerId) {
        const customerExist = await checkCustomerExists(
          customerId,
          stripeBusinessAccountId
        );
        if (customerExist.status == 200) {
          const customerPaymentDetails = await fetchCustomerPaymentCards(
            customerExist,
            stripeBusinessAccountId
          );
          return {
            status: 200,
            message: "Success",
            response: customerPaymentDetails.response,
          };
        } else {
          return { status: 404, message: "Customer not Exist", response: {} };
        }
      }
      return { status: 404, message: "Customer ID is required", response: {} };
    }
    return { status: 404, message: "No Integrations found", response: {} };
  } catch (error) {
    // Handle errors
    console.error(error);
    return { status: 400, response: error }; // Rethrow the error to be handled by the caller
  }
};

module.exports.addCustomerCardDetails = async (
  businessId,
  customerId,
  paymentDetails
) => {
  try {
    const fetchIntegrationData = await integrationModel.getIntegrationDetails(
      businessId,
      "payment",
      8,
      true
    );
    if (fetchIntegrationData) {
      const { token } = paymentDetails;
      let stripeBusinessAccountId = fetchIntegrationData.username;
      const stripeBusinessAccountSettings =
        JSON.parse(fetchIntegrationData.settings) || {};

      stripeBusinessAccountId = await getBranchWiseAccount(
        stripeBusinessAccountSettings,
        stripeBusinessAccountId,
        0
      );

      if (customerId) {
        const customerExist = await checkCustomerExists(
          customerId,
          stripeBusinessAccountId
        );
        if (customerExist.status == 200) {
          const customerPaymentDetails = await addCardToCustomer(
            customerId,
            token,
            stripeBusinessAccountId
          );
          return {
            status: customerPaymentDetails.status,
            message: customerPaymentDetails.message,
            response: customerPaymentDetails.response,
          };
        } else {
          return { status: 404, message: "Customer not Exist", response: {} };
        }
      }
      return { status: 404, message: "Customer ID is required", response: {} };
    }
    return { status: 404, message: "No Integrations found", response: {} };
  } catch (error) {
    // Handle errors
    console.error(error);
    return { status: 400, response: error }; // Rethrow the error to be handled by the caller
  }
};

const getBranchWiseAccount = async (
  stripeBusinessAccountSettings,
  stripeBusinessAccountId,
  branchId
) => {
  try {
    if (
      stripeBusinessAccountSettings &&
      stripeBusinessAccountSettings.branchwise_payments
    ) {
      const branchwiseSettings =
        stripeBusinessAccountSettings.branchwise_settings || {};

      if (branchId !== 0 && branchwiseSettings[branchId]) {
        stripeBusinessAccountId = branchwiseSettings[branchId].account_id;
        // Now you have the account_id for the branch
      }
      return stripeBusinessAccountId;
    } else {
      return stripeBusinessAccountId;
    }
  } catch (error) {
    throw error;
  }
};

const addCustomerWithCard = async (
  saveCard,
  customerExist,
  paymentDetails,
  stripeBusinessAccountId
) => {
  try {
    let customerDetails = "";
    if (saveCard && !customerExist.valid) {
      customerExist = await stripe.customers.create(
        {
          email: paymentDetails.email,
          name: paymentDetails.name,
          source: paymentDetails.token, // Payment token obtained from Stripe.js
        },
        {
          stripeAccount: stripeBusinessAccountId,
        }
      );
      return { status: 200, message: "Success", response: customerExist };
      return customerExist;
    }
    return { status: 400, message: "Something went wrong", response: {} };
  } catch (error) {
    return { status: 400, message: error.message, response: {} };
  }
};

const checkCustomerExists = async (id, stripeBusinessAccountId) => {
  try {
    const customers = await stripe.customers.retrieve(id, {
      stripeAccount: stripeBusinessAccountId,
    });
    if (customers) {
      return { status: 200, message: "Success", response: customers }; // Customer exists
    } else {
      return { status: 404, message: "No Custoemers Found", response: {} }; // Customer does not exist
    }
  } catch (error) {
    return { status: error.status, message: error.message, response: {} };
  }
};

const fetchCustomerPaymentCards = async (customer, stripeBusinessAccountId) => {
  try {
    const paymentMethod = await stripe.paymentMethods.list(
      {
        customer: customer.response.id,
        type: "card",
      },
      {
        stripeAccount: stripeBusinessAccountId,
      }
    );
    if (paymentMethod.data.length > 0) {
      let paymentMethodDetails = [];
      const listOfPaymentMethods = paymentMethod.data;
      listOfPaymentMethods.forEach((paymentMethod) => {
        const cardDetail = {
          id: paymentMethod.id,
          brand: paymentMethod.card.brand,
          country: paymentMethod.card.country,
          exp_month: paymentMethod.card.exp_month,
          exp_year: paymentMethod.card.exp_year,
          last4: paymentMethod.card.last4,
        };
        paymentMethodDetails.push(cardDetail);
      });
      return {
        status: 200,
        message: "Success",
        response: paymentMethodDetails,
      }; // Customer exists
    } else {
      return { status: 404, message: "No Custoemers Found", response: {} }; // Customer does not exist
    }
  } catch (error) {
    return { status: error.status, message: error.message, response: {} };
  }
};

const addCardToCustomer = async (
  customerId,
  cardToken,
  stripeBusinessAccountId
) => {
  try {
    const attachedCard = await stripe.customers.createSource(
      customerId,
      {
        source: cardToken,
      },
      {
        stripeAccount: stripeBusinessAccountId,
      }
    );
    if (attachedCard) {
      return { status: 200, message: "Success", response: attachedCard };
    } else {
      return { status: 400, message: "Card not added", response: {} }; // Customer does not exist
    }
  } catch (error) {
    console.error("Error:", error);
    throw error;
  }
};

const createCustomer = async (businessId, userId, email, customerId) => {
  userId = userId || 0;
  const apiURL =
    process.env.WEB_API_URL +
    "business/" +
    businessId +
    "/user/" +
    userId +
    "/payment/id";
  const headers = {};
  const requestBody = {
    id: userId,
    business_id: businessId,
    payment_type: "stripe",
    email: email,
    customer_id: customerId,
  };
  const createCustomerPayment = await client.put(apiURL, requestBody, {
    headers: headers,
  });
};
