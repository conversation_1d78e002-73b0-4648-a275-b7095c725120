const Joi = require("joi");

module.exports.validateOrderJson = (data) => {
  const { error, value } = orderSchema.validate(data, {
    allowUnknown: true,
    abortEarly: false,
  });

  if (error) {
    throw new Error(`Validation Error: ${error.message}`);
  }

  return value;
};

module.exports.validateBulkOrderJson = (data) => {
  const { error, value } = bulkOrderSchema.validate(data, {
    allowUnknown: true,
    abortEarly: false,
  });

  if (error) {
    console.log(error);
    throw new Error(`Validation Error: ${error.message}`);
  }

  return value;
};

module.exports.validateGetOrderJson = (data) => {
  const { error, value } = getOrderSchema.validate(data, {
    allowUnknown: false,
    abortEarly: false,
  });

  if (error) {
    console.log(error);
    throw new Error(`Validation Error: ${error.message}`);
  }

  return value;
};

const orderSchema = Joi.object({
  orderid: Joi.number().required(),
  td_order_id: Joi.number().required(),
  temp_order_id: Joi.string().required(),
  business_id: Joi.number().required(),
  uid: Joi.number().allow(""),
  name: Joi.string().required(),
  mobile_phone: Joi.string().required(),
  city: Joi.string().required(),
  area: Joi.string().required(),
  source: Joi.string().required(),
  address: Joi.string().required(),
  total: Joi.number().required(),
  tax: Joi.number().required(),
  tax_value: Joi.number().required(),
  discount: Joi.number().required(),
  discount_value: Joi.number().required(),
  gtotal: Joi.number().required(),
  ordertype: Joi.string().allow(""),
  note: Joi.string().allow(""),
  email: Joi.string().required(),
  delivery_charges: Joi.number().required(),
  delivery_tax: Joi.number().required(),
  delivery_tax_value: Joi.number().required(),
  bid: Joi.number().required(),
  delivery: Joi.string().required(),
  delivery_date: Joi.string().required(),
  postal_code: Joi.string().allow(""),
  custom_code: Joi.string().allow(""),
  custom_code_id: Joi.number().required(),
  custom_code_type: Joi.string().required(),
  custom_code_discount: Joi.number().required(),
  custom_code_discount_value: Joi.number().required(),
  user_latitude: Joi.string().allow(""),
  user_longitude: Joi.string().allow(""),
  order_type: Joi.string().allow(""),
  order_type_flag: Joi.string().required(),
  tip: Joi.number().required(),
  service_charges: Joi.number().required(),
  cnic: Joi.string().allow(""),
  canada_post: Joi.number().required(),
  loyalty_points: Joi.number().required(),
  items: Joi.array().items(
    Joi.object({
      odetailid: Joi.number().required(),
      orderid: Joi.number().required(),
      name: Joi.string().required(),
      qty: Joi.number().required(),
      price: Joi.number().required(),
      total: Joi.number().required(),
      item_level_grand_total: Joi.number().required(),
      comment: Joi.string().allow(""),
      option_set: Joi.object().default({}),
      id: Joi.number().required(),
      discount: Joi.number().required(),
      item_level_discount_value: Joi.number().required(),
      coupon_discount: Joi.number().required(),
      coupon_discount_value: Joi.number().required(),
      adjustment_amount: Joi.number().required(),
      tax: Joi.number().required(),
      item_level_tax_value: Joi.number().required(),
      weight_value: Joi.string().allow(""),
      weight_unit: Joi.string().allow(""),
      category_id: Joi.number().required(),
      brand_id: Joi.number().required(),
      product_code: Joi.string().allow(""),
      category_name: Joi.string().allow(""),
    })
  ),
});

const orderItemSchema = Joi.object({
  id: Joi.number().required().allow(0),
  name: Joi.string().required(),
  qty: Joi.number().required(),
  price: Joi.number().required(),
  total: Joi.number().required(),
  item_level_grand_total: Joi.number().required(),
  comment: Joi.string().allow(""),
  option_set: Joi.object().default({}),
  discount: Joi.string().allow(""),
  item_level_discount_value: Joi.number().required(),
  coupon_discount: Joi.number().required(),
  coupon_discount_value: Joi.number().required(),
  tax: Joi.string().allow(""),
  item_level_tax_value: Joi.number().required(),
  weight_value: Joi.string().allow(""),
  weight_unit: Joi.string().allow(""),
  category_id: Joi.number().required().allow(0),
  brand_id: Joi.number().allow(""),
  product_code: Joi.string().allow(""),
  category_name: Joi.string().allow(""),
});

const bulkOrderSchema = Joi.object({
  orders: Joi.array()
    .items(
      Joi.object({
        order_id: Joi.number().allow(""),
        business_id: Joi.number().required(),
        uid: Joi.number().required(),
        name: Joi.string().allow(""),
        mobile_phone: Joi.string().allow(""),
        city: Joi.string().allow(""),
        area: Joi.string().allow(""),
        source: Joi.string().required(),
        address: Joi.string().allow(""),
        total: Joi.number().required(),
        tax: Joi.number().required(),
        tax_value: Joi.number().required(),
        discount: Joi.number().required(),
        discount_value: Joi.number().required(),
        gtotal: Joi.number().required(),
        order_type: Joi.string().allow(""),
        note: Joi.string().allow(""),
        email: Joi.string().allow(""),
        delivery_charges: Joi.number().required(),
        delivery_tax: Joi.number().required(),
        delivery_tax_value: Joi.number().required(),
        bid: Joi.number().required(),
        delivery: Joi.string().allow(""),
        delivery_date: Joi.string().required(),
        postal_code: Joi.string().allow(""),
        custom_code: Joi.string().allow(""),
        custom_code_id: Joi.number().required(),
        custom_code_type: Joi.string().required(),
        custom_code_discount: Joi.number().required(),
        custom_code_discount_value: Joi.number().required(),
        user_latitude: Joi.string().allow(""),
        user_longitude: Joi.string().allow(""),
        order_type: Joi.string().allow(""),
        order_type_flag: Joi.string().required(),
        tip: Joi.number().required(),
        service_charges: Joi.number().required(),
        cnic: Joi.string().allow(""),
        canada_post: Joi.number().required(),
        payment_type: Joi.number().required(),
        last_sync: Joi.string().allow(""),
        last_update: Joi.string().required(),
        loyalty_points: Joi.number().required(),
        guests: Joi.string().allow(""),
        cash_change: Joi.number().required(),
        cash_received: Joi.number().required(),
        eatout_manager_id: Joi.number().required(),
        is_paid: Joi.number().required(),
        pos_id: Joi.string().allow(""),
        tax_type: Joi.string().allow(""),
        is_partial_refund: Joi.number().required(),
        table_no: Joi.string().allow(""),
        status: Joi.string().required(),
        manager_name: Joi.string().required(),
        is_splitted: Joi.string().required(),
        splitted_payments: Joi.array().required(),
        receipt_no: Joi.string().required(),
        pay_owner: Joi.string().allow(""),
        pay_type: Joi.string().allow(""),
        items: Joi.array().items(orderItemSchema).required(),
      })
    )
    .required(),
});

const getOrderSchema = Joi.object({
  to_date: Joi.date().iso().optional(),
  from_date: Joi.date().iso().optional(),
  business_id: Joi.string().required(),
  order_id: Joi.string().allow(""),
  order_source: Joi.string().allow(""),
  source: Joi.string().required(),
  offset: Joi.number().allow(""),
  limit: Joi.number().allow(""),
  location_id: Joi.string().allow(""),
  is_paid: Joi.string().allow(""),
  payment_type: Joi.string().allow(""),
});
