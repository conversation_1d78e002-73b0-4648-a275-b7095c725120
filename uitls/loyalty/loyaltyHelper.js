const integrationModel = require('../../models/integrationModel');
const client = require("../client")
const dotenv = require('dotenv'); // Importing dotenv for environment variables
dotenv.config(); // Loading environment variables from .env file

module.exports.createLoyaltyRedemptionRequest = async (businessId, orderData) => {

    try {

        const createLoyaltyPayload = await createSendgridPayload(businessId, orderData);

        const apiURL = process.env.LOYALTY_API_URL + "/loyalty/customer/" + createLoyaltyPayload.source_id + "/redeem"
        if (createLoyaltyPayload.source_id != 0) {
            const createLoyaltyRequest = await createAPI(businessId, apiURL, createLoyaltyPayload);
            console.log(createLoyaltyRequest);
        }

    } catch (error) {
        // Handle errors
        console.error("Error fetching payment details:", error.data);
        throw error; // Rethrow the error to be handled by the caller
    }

}
const createSendgridPayload = async (businessId, orderData) => {

    const itemsArray = orderData.order_details.map(item => ({
        name: item.dname,
        quantity: item.dqty,
        amount: item.dtotal,
        id: item.menu_item_id,
        discount: item.discount,
        category_id: item.category_id,
        brand_id: item.brand_id || 0,
    }));

    const requestBody = {
        name: orderData.name,
        email: orderData.email,
        mobile_phone: orderData.mobile,
        business_id: businessId,
        source_id: orderData.uid,
        order_id: orderData.order_id,
        bid: orderData.bid,
        tax: orderData.tax.toString(),
        tax_value: orderData.tax_value.toString(),
        discount: orderData.discount.toString(),
        discount_value: orderData.discount_value.toString(),
        order_type: orderData.order_type,
        delivery_charges: orderData.delivery_charges.toString(),
        voucher: "",
        voucher_discount: "",
        loyalty_points: orderData.loyalty_points,
        voucher_discount_value: "",
        postal_code: orderData.postal_code,
        total: orderData.total,
        order_details: itemsArray,
        meta_data: "{}"
    }
    return requestBody;
}

const createAPI = async (businessId, apiURL, orderData) => {

    const fetchIntegrationData = await integrationModel.getIntegrationDetails(businessId, 'loyalty_program', 21, true);
    if (fetchIntegrationData) {

        const loyaltySettings = JSON.parse(fetchIntegrationData.settings);
        if (loyaltySettings && loyaltySettings.campaign_status == true) {
            const headers = {}

            // Sending the request to Pusher
            const response = await client.post(apiURL, orderData, { headers: headers });

            const responseBody = { status: 200, message: "Success", result: response.data }
            return responseBody;
        }
        const responseBody = { status: 404, message: "Not found", result: {} }
        return responseBody;
    }
    const responseBody = { status: 404, message: "Not integration found", result: {} }
    return responseBody;

}