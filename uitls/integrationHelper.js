const integrationModel = require('../models/integrationModel');


module.exports.getPaymentMethods = async (businessId, businessSettings) => {

    try {

        const paymentDetails = await integrationModel.getIntegrationDetails(businessId, 'payment');

        let paymentOptions = []

        const cod = {
            name: "Cash On Delivery",
            enabled: true,
            method: "cod",
            account_id: ""
        }

        if (!('cod' in businessSettings.settings) || businessSettings.settings.cod === 1) {
            paymentOptions.push(cod);
        }

        const paymentCount = paymentDetails.length;
        paymentDetails.forEach(item => {
            const { name, username } = item;
            let paymentName = "Debit / Credit Card";

            if (paymentCount > 1) {
                paymentName = "Debit / Credit Card (" + uppercaseFirstChar(name) +")"
            }
            const response = {
                name: paymentName,
                enabled: true,
                method: name,
                account_id: (username) ? username : ""
            };

            paymentOptions.push(response);
        });

        return paymentOptions;

    } catch (error) {
        // Handle errors
        console.error("Error fetching payment details:", error);
        throw error; // Rethrow the error to be handled by the caller
    }

}

module.exports.getAddressAPI = async (businessId) => {

    try {

        let key = "";
        let apiDetails = await integrationModel.getIntegrationDetails(businessId, 'address', 43);

        if (apiDetails.length > 0) {
            key = apiDetails[0].key;
        }

        return key;

    } catch (error) {
        // Handle errors
        console.error("Error fetching payment details:", error);
        throw error; // Rethrow the error to be handled by the caller
    }

}

function uppercaseFirstChar(inputString) {
    if (inputString.length > 0) {
        return inputString.charAt(0).toUpperCase() + inputString.slice(1);
    } else {
        return inputString;
    }
}