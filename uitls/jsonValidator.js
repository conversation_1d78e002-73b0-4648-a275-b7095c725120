const Joi = require('joi');

module.exports.validatePaymentJson = (data) => {
  const { error, value } = paymentSchema.validate(data, { allowUnknown: false, abortEarly: false });

  if (error) {
    throw new Error(`Validation Error: ${error.message}`);
  }

  return value;
};

module.exports.validateRegisterStripeCustomerJson = (data) => {
  const { error, value } = registerStripeCustomerSchema.validate(data, { allowUnknown: false, abortEarly: false });

  if (error) {
    throw new Error(`Validation Error: ${error.message}`);
  }

  return value;
};


const paymentSchema = Joi.object({
  order_id: Joi.number().required(),
  business_id: Joi.number().required(),
  currency_code: Joi.string().required(),
  branch_id: Joi.number().required(),
  email: Joi.string().email().required(),
  amount: Joi.number().required(),
  pre_auth: Joi.string().valid('true', 'false').required(),
  source: Joi.string().valid('web', 'app').required(),
  return_url: Joi.string().uri().allow(''),
  payment_type: Joi.string().required(),
  token: Joi.string().when('payment_type', {
    is: 'stripe',
    then: Joi.string().required(),
    otherwise: Joi.string().allow('')
  }),
  save_card: Joi.boolean().allow(''),
  name: Joi.string().when('save_card', {
    is: true,
    then: Joi.string().required(),
    otherwise: Joi.string().allow('')
  }),
  // customer_id: Joi.string().allow("")
  customer_id: Joi.string().when('payment_type', {
    is: 'stripe',
    then: Joi.string().allow("").when('token', {
      not: Joi.string().regex(/^(?!tok_)/),
      then: Joi.string().required(),
      otherwise: Joi.string().allow('')
    }),
    otherwise: Joi.string().allow('')
  }),
  user_id: Joi.string().when('payment_type', {
    is: 'stripe',
    then: Joi.string().allow("").when('token', {
      not: Joi.string().regex(/^(?!tok_)/),
      then: Joi.string().required(),
      otherwise: Joi.string().allow('')
    }),
    otherwise: Joi.string().allow('')
  })
});

const registerStripeCustomerSchema = Joi.object({
  order_id: Joi.number().min(0).required(),
  business_id: Joi.number().required(),
  branch_id: Joi.number().required(),
  email: Joi.string().email().required(),
  amount: Joi.number().required(),
  pre_auth: Joi.string().valid('true', 'false').required(),
  source: Joi.string().valid('web', 'app').required(),
  return_url: Joi.string().uri().allow(''),
  payment_type: Joi.string().required(),
  token: Joi.string().when('payment_type', {
    is: 'stripe',
    then: Joi.string().required(),
    otherwise: Joi.string().allow('')
  }),
  save_card: Joi.boolean().allow(''),
  name: Joi.string().when('save_card', {
    is: true,
    then: Joi.string().required(),
    otherwise: Joi.string().allow('')
  }),
  // customer_id: Joi.string().allow("")
  customer_id: Joi.string().when('payment_type', {
    is: 'stripe',
    then: Joi.string().allow("").when('token', {
      not: Joi.string().regex(/^(?!tok_)/),
      then: Joi.string().required(),
      otherwise: Joi.string().allow('')
    }),
    otherwise: Joi.string().allow('')
  }),
  user_id: Joi.string().when('payment_type', {
    is: 'stripe',
    then: Joi.string().allow("").when('token', {
      not: Joi.string().regex(/^(?!tok_)/),
      then: Joi.string().required(),
      otherwise: Joi.string().allow('')
    }),
    otherwise: Joi.string().allow('')
  })
});
