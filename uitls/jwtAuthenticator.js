const jwt = require('jsonwebtoken');
const dotenv = require('dotenv');
dotenv.config();


const secretKey = process.env.JWT_SECRET; // You should store this securely, preferably in an environment variable

// Function to verify a JWT token
module.exports.verifyToken = async (token) => {
    try {
        const decoded = jwt.verify(token, secretKey);
        return { valid: true, message: "Success", result: decoded };
    } catch (error) {
        return { valid: false, message: error.message, result:{} };
    }
}