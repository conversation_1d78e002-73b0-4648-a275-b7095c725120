const moment = require('moment');

module.exports.generateTimeSlots = async (dayNumber, pickupSchedule) => {
    const dayTiming = pickupSchedule[dayNumber.toString()]; // Get timing for the provided day number
    if (!dayTiming || dayTiming.length === 0) {
        return []; // Return empty array if no timing is available for the day
    }

    const day = moment(); // Get current moment object
    const slots = [];

    // Loop through each timing range of the day
    dayTiming.forEach(timing => {
        const startTime = moment(timing.timing[1][0], 'HH:mm'); // Get start time
        const endTime = moment(timing.timing[1][1], 'HH:mm'); // Get end time

        // Set start time for the day
        day.startOf('day').set({
            hour: startTime.hours(),
            minute: startTime.minutes()
        });

        // Loop through each 30-minute interval of the day within the specified timing range
        while (day.isSameOrBefore(endTime)) {
            const slotEndTime = day.clone().add(30, 'minutes');
            slots.push({
                start: day.format('HH:mm'),
                end: slotEndTime.format('HH:mm')
            });
            day.add(30, 'minutes'); // Move to the next 30-minute interval
        }
    });


    return slots;
}