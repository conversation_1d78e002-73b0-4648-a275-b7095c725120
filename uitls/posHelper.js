const ordersModel = require('../models/ordersModel'); // Importing Axios for making HTTP requests
const axios = require('axios'); // Importing Axios for making HTTP requests
const dotenv = require('dotenv'); // Importing dotenv for environment variables
dotenv.config(); // Loading environment variables from .env file

// Function to send Pusher notification for a new order
module.exports.posOrderDetails = async (businessId, orderDetails) => {
    try {
        const stringifiedOrderDetails = stringifyValuesAndReplaceKey(orderDetails);
        const orderDataSettings = {
            message: "Order status changed!",
            transaction_id: "'"+orderDetails.order_id+"'",
            eatout_id: businessId.toString(),
            branch_id: orderDetails.bid.toString(),
            type: "order",
            source: "web",
            order: JSON.stringify(stringifiedOrderDetails)
        }

        const posNotificationDetails = {
            created_at: new Date(),
            updated_at: new Date(),
            order_id: orderDetails.order_id,
            time_zone: orderDetails.time_zone,
            channel: businessId + "-" + orderDetails.bid,
            order_data: JSON.stringify(orderDataSettings),
        };


        const checkNotificationEntry = await ordersModel.checkPosNotifcationEntry(orderDetails.order_id);
        if(checkNotificationEntry.count == 0){
            const cartResponse = await ordersModel.addPosNotifcation(posNotificationDetails);
        } else {
            const cartResponse = await ordersModel.updatePosNotifcation(orderDetails.order_id, posNotificationDetails);
        }

    } catch (error) {
        // Logging any errors that occur during the try block
        console.error('Error in sendPusherNotification:', error.message);
        // Returning the error
        return error;
    }
}


// Convert all values to strings
const stringifyValuesAndReplaceKey = (obj) => {
    const stringifiedObj = {};
    for (const key in obj) {
      if (key === "items") {
        stringifiedObj["order_detail"] = obj[key].map(item => stringifyValuesAndReplaceKey(item));
      } else if (typeof obj[key] === 'object' && !Array.isArray(obj[key])) {
        stringifiedObj[key] = stringifyValuesAndReplaceKey(obj[key]);
      } else {
        stringifiedObj[key] = String(obj[key]);
      }
    }
    return stringifiedObj;
  };