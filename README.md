# README

This README would normally document whatever steps are necessary to get your application up and running.

### What is this repository for?

- Quick summary
- Version
- [Learn Markdown](https://bitbucket.org/tutorials/markdowndemo)

### How do I get set up?

- Summary of set up
- Configuration
- Dependencies
- Database configuration
- How to run tests
- Deployment instructions

### Contribution guidelines

- Writing tests
- Code review
- Other guidelines

### Who do I talk to?

- Repo owner or admin
- Other community or team contact

## Deployment

### Automated Deployment with Bitbucket Pipelines

This project includes a Bitbucket Pipelines configuration for automated deployments to AWS. The pipeline will:

1. Deploy to the development environment when pushing to the `develop` branch
2. Deploy to the production environment when pushing to the `main` branch
3. Provide manual deployment options via custom pipelines

#### Setup Requirements:

1. Enable Bitbucket Pipelines in your repository settings
2. Add the following repository variables in Bitbucket (Repository Settings > Repository Variables):

   - `AWS_ACCESS_KEY_ID`: Your AWS access key
   - `AWS_SECRET_ACCESS_KEY`: Your AWS secret key
   - `AWS_DEFAULT_REGION`: us-east-1 (or your preferred region)

3. Ensure your AWS IAM user has the following permissions:
   - Lambda full access
   - API Gateway full access
   - CloudFormation full access
   - S3 access
   - IAM role creation permissions
   - CloudWatch Logs access
