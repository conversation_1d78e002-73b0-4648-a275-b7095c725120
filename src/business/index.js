const response = require("../../uitls/response");
const businessModal = require("../../models/businessModel");
const timeSlotHelper = require("../../uitls/timeSlotHelper");
const paymentsHelper = require("../../uitls/integrationHelper");
const moment = require('moment');

// Set default headers
const headers = {
    "Content-Type": "application/json",
    "Access-Control-Allow-Origin": "*", // or specify your allowed origin
    "Access-Control-Allow-Headers": "Origin, X-Requested-With, Content-Type, Accept",
    "Access-Control-Allow-Methods": "OPTIONS, POST, GET, PUT, DELETE",
};


// Function to get business settings
module.exports.getBusinessSetting = async (event) => {
    // Extract business ID from path parameters

    try {
        const businessId = event.pathParameters.business_id;
        if (businessId == 0 || businessId == "") {

            return response.createResponse(400, headers, "Business id is required", {});
        }
        // Fetch business details using the business ID
        let businessSettings = await businessModal.getBusinessDetails(businessId);
        // const paymentSettings = await paymentsHelper.getPaymentMethods(businessId);

        businessSettings.logo = "https://static.tossdown.com/logos/"+businessSettings.logo;
        // businessSettings.payment_options = paymentSettings;

        // Return success response
        return response.createResponse(200, headers, "Success", businessSettings);
    } catch (err) {
        // Return error response in case of an exception
        return response.createResponse(400, headers, err, {});
    }
};

// Function to get branch settings
module.exports.getBranchSetting = async (event) => {
    try {
        // Extract business and location IDs from path parameters
        const businessId = event.pathParameters.business_id;
        const locationId = event.pathParameters.location_id || 0;

        // Fetch business details using the business ID
        const businessSettings = await businessModal.getBusinessDetails(businessId);
        // Fetch branch details using business and location IDs
        const branchSettings = await businessModal.getBranchDetails(
            businessId,
            locationId
        );

        const businessSettingsMapped = branchSettings.map(item => ({
            ...item,
            settings: item.settings || JSON.stringify({"cart": {"tax": "", "discount": "", "tax_type": "", "minimum_spent": "", "online_payment_tax": ""}}) /// If item.settings is null, set it to {}
        }));
        const paymentSettings = await paymentsHelper.getPaymentMethods(businessId, businessSettings);
        const addressAPI = await paymentsHelper.getAddressAPI(businessId);
        
        let result = businessSettings;
        result.logo = "https://static.tossdown.com/logos/"+businessSettings.logo;
        result.payment_options = paymentSettings;
        result.address_api = addressAPI;
        result.branches = businessSettingsMapped;

        // Return success response
        return response.createResponse(200, headers, "Success", result);
    } catch (err) {
        // Return error response in case of an exception
        return response.createResponse(400, headers, "An error occured", err);
    }
};

// Function to get branch settings
module.exports.getBranchPickupHours = async (event) => {
    try {
        // Extract business and location IDs from path parameters
        const businessId = event.pathParameters.business_id;
        const locationId = event.pathParameters.location_id || 0;
        const utcTime = moment.utc();

        // Fetch business details using the business ID
        const businessSettings = await businessModal.getBusinessDetails(businessId);

        // Fetch branch details using business and location IDs
        const branchSettings = await businessModal.getBranchDetails(
            businessId,
            locationId
        );
        const preparationTime = businessSettings.preparation_time;
        const date = new Date('2022-02-04');
        const singleBranchSettings = branchSettings[0]
        const getSpecificTiming = JSON.parse(singleBranchSettings.timing);
  
        const pickupTiming = getSpecificTiming.pickup || {};
        day = '1';
        const pickupDayTiming= pickupTiming[day] || [];
        const getSpecificTimeZone = singleBranchSettings.time_zone;

        const convertedTime = utcTime.clone().utcOffset(getSpecificTimeZone);
        const currentTime = convertedTime.format();

        const result = await timeSlotHelper.generateTimeSlots(1, pickupTiming);

        // Return success response
        return response.createResponse(200, headers, "Success", result);
    } catch (err) {
        // Return error response in case of an exception
        return response.createResponse(400, headers, "An error occured", err);
    }
};
