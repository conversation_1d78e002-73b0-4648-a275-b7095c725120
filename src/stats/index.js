const connection = require('../../config/database');
const businessModal = require("../../models/businessModel");

// Set default headers
const headers = {
    "Content-Type": "application/json",
    "Access-Control-Allow-Origin": "*", // or specify your allowed origin
    "Access-Control-Allow-Headers": "Origin, X-Requested-With, Content-Type, Accept",
    "Access-Control-Allow-Methods": "OPTIONS, POST, GET, PUT, DELETE",
};
exports.getMetrics = async (event, context) => {
    const businessId = event.pathParameters.business_id;
    const { startDate, endDate } = event.queryStringParameters;

    let businessSettings = await businessModal.getBusinessDetails(businessId);
    const time_zone = businessSettings.time_zone;

    const queries = {
        totalSales: `SELECT IFNULL(SUM(gtotal), 0) AS totalSales FROM orders WHERE r_id = ? AND binary_status NOT IN ('Cancel', 'Viewed', 'Pending') AND CONVERT_TZ(date, @@session.time_zone, ?) BETWEEN ? AND ?`,
        totalOrders: `SELECT IFNULL(COUNT(*), 0) AS totalOrders FROM orders WHERE r_id = ? AND binary_status NOT IN ('Cancel', 'Viewed', 'Pending') AND CONVERT_TZ(date, @@session.time_zone, ?) BETWEEN ? AND ?`,
        totalCustomers: `SELECT IFNULL(COUNT(DISTINCT email), 0) AS totalCustomers FROM orders WHERE r_id = ? AND CONVERT_TZ(date, @@session.time_zone, ?) BETWEEN ? AND ?`,
        averageOrderValue: `SELECT IFNULL(AVG(gtotal), 0) AS aov FROM orders WHERE r_id = ? AND binary_status NOT IN ('Cancel', 'Viewed', 'Pending') AND CONVERT_TZ(date, @@session.time_zone, ?) BETWEEN ? AND ?`,
        pendingSales: `SELECT IFNULL(SUM(gtotal), 0) AS pendingSales FROM orders WHERE r_id = ? AND status = "Pending" AND payment_type != 3 AND CONVERT_TZ(date, @@session.time_zone, ?) BETWEEN ? AND ?`,
        pendingOrders: `SELECT IFNULL(COUNT(*), 0) AS pendingOrders FROM orders WHERE r_id = ? AND status = "Pending" AND payment_type != 3 AND CONVERT_TZ(date, @@session.time_zone, ?) BETWEEN ? AND ?`,
        cancelledSales: `SELECT IFNULL(SUM(gtotal), 0) AS cancelledSales FROM orders WHERE r_id = ? AND binary_status = "Cancel" AND CONVERT_TZ(date, @@session.time_zone, ?) BETWEEN ? AND ?`,
        cancelledOrders: `SELECT IFNULL(COUNT(*), 0) AS cancelledOrders FROM orders WHERE r_id = ? AND binary_status = "Cancel" AND CONVERT_TZ(date, @@session.time_zone, ?) BETWEEN ? AND ?`,
        dailyMetrics: `SELECT DATE(CONVERT_TZ(date, @@session.time_zone, ?)) AS day, 
                          IFNULL(SUM(CASE WHEN status NOT IN ('Cancel', 'Viewed', 'Pending') THEN gtotal ELSE 0 END), 0) AS dailySales, 
                          IFNULL(COUNT(CASE WHEN status NOT IN ('Cancel', 'Viewed', 'Pending') THEN 1 ELSE NULL END), 0) AS dailyOrders,
                          IFNULL(COUNT(DISTINCT email), 0) AS totalCustomers,
                          IFNULL(AVG(CASE WHEN status NOT IN ('Cancel', 'Viewed', 'Pending') THEN gtotal ELSE NULL END), 0) AS dailyAOV,
                          IFNULL(SUM(CASE WHEN status = 'Pending' AND payment_type != 3 THEN gtotal ELSE 0 END), 0) AS dailyPendingSales,
                          IFNULL(COUNT(CASE WHEN status = 'Pending' AND payment_type != 3 THEN 1 ELSE NULL END), 0) AS dailyPendingOrders,
                          IFNULL(SUM(CASE WHEN status = 'Cancel' THEN gtotal ELSE 0 END), 0) AS dailyCancelledSales,
                          IFNULL(COUNT(CASE WHEN status = 'Cancel' THEN 1 ELSE NULL END), 0) AS dailyCancelledOrders
                   FROM orders 
                   WHERE r_id = ? 
                   AND CONVERT_TZ(date, @@session.time_zone, ?) BETWEEN ? AND ? 
                   GROUP BY day 
                   ORDER BY day`,
        heatMap: `SELECT DATE(CONVERT_TZ(date, @@session.time_zone, ?)) AS day, user_latitude, user_longitude 
                FROM orders 
                WHERE r_id = ? 
                AND status NOT IN ('Cancel', 'Viewed', 'Pending') 
                AND CONVERT_TZ(date, @@session.time_zone, ?) BETWEEN ? AND ? 
                AND user_latitude IS NOT NULL 
                AND user_longitude IS NOT NULL
                AND user_latitude != '' 
                AND user_longitude != ''`,          
        dailyMetricsBySource: `SELECT DATE(CONVERT_TZ(date, @@session.time_zone, ?)) AS day, 
                                  source,
                                  IFNULL(SUM(CASE WHEN status NOT IN ('Cancel', 'Viewed', 'Pending') THEN gtotal ELSE 0 END), 0) AS dailySales, 
                                  IFNULL(COUNT(CASE WHEN status NOT IN ('Cancel', 'Viewed', 'Pending') THEN 1 ELSE NULL END), 0) AS dailyOrders,
                                  IFNULL(COUNT(DISTINCT email), 0) AS totalCustomers,
                                  IFNULL(AVG(CASE WHEN status NOT IN ('Cancel', 'Viewed', 'Pending') THEN gtotal ELSE NULL END), 0) AS dailyAOV,
                                  IFNULL(SUM(CASE WHEN status = 'Pending' AND payment_type != 3 THEN gtotal ELSE 0 END), 0) AS dailyPendingSales,
                                  IFNULL(COUNT(CASE WHEN status = 'Pending' AND payment_type != 3 THEN 1 ELSE NULL END), 0) AS dailyPendingOrders,
                                  IFNULL(SUM(CASE WHEN status = 'Cancel' THEN gtotal ELSE 0 END), 0) AS dailyCancelledSales,
                                  IFNULL(COUNT(CASE WHEN status = 'Cancel' THEN 1 ELSE NULL END), 0) AS dailyCancelledOrders
                           FROM orders 
                           WHERE r_id = ? 
                           AND CONVERT_TZ(date, @@session.time_zone, ?) BETWEEN ? AND ? 
                           GROUP BY day, source 
                           ORDER BY day, source`
    };

    const metrics = {};

    const runQueries = (query, key, params) => {
        return new Promise((resolve, reject) => {
            console.log(`Running query for ${key}`);
            const startTime = Date.now();

            connection.query(query, params, (err, results) => {
                const endTime = Date.now();
                console.log(`Query for ${key} took ${endTime - startTime}ms`);

                if (err) return reject(err);
                metrics[key] = results; // Save the entire result set
                resolve();
            });
        });
    };

    try {
        await Promise.all(Object.keys(queries).map(key => {
            const query = queries[key];
            let params;
            if (key === 'dailyMetrics' || key === 'dailyMetricsBySource' || key === 'heatMap') {
                params = [time_zone, businessId, time_zone, startDate, endDate];
            } else {
                params = [businessId, time_zone, startDate, endDate];
            }
            return runQueries(query, key, params);
        }));

        // Function to remove keys with a value of 0
        const removeZeroKeys = (obj) => {
            return Object.fromEntries(
                Object.entries(obj).filter(([_, v]) => v !== 0)
            );
        };
        // Format daily metrics into an array of objects if it exists
        const dailyMetrics = metrics.dailyMetrics ? metrics.dailyMetrics.map(row => {
            const cleanedRow = removeZeroKeys({
                date: row.day,
                totalOrders: row.dailyOrders,
                totalSales: row.dailySales,
                totalCustomers: row.totalCustomers,
                averageOrderValue: row.dailyAOV,
                pendingOrders: row.dailyPendingOrders,
                pendingSales: row.dailyPendingSales,
                cancelledOrders: row.dailyCancelledOrders,
                cancelledSales: row.dailyCancelledSales,
            });
            return Object.keys(cleanedRow).length > 1 ? cleanedRow : null; // Return null if only the date remains
        }).filter(row => row !== null) : [];

        const heatMap = metrics.heatMap ? metrics.heatMap.map(row => ({
            userLat: parseFloat(row.user_latitude),
            userLng: parseFloat(row.user_longitude),
        })) : [];

        // Group dailyMetricsBySource by date and encapsulate source data
        const dailyMetricsBySource = metrics.dailyMetricsBySource ? Object.values(metrics.dailyMetricsBySource.reduce((acc, row) => {

            if (!acc[row.day]) {
                acc[row.day] = {
                    date: row.day,
                    sources: []
                };
            }
            
            const cleanedSourceData = removeZeroKeys({
                source: row.source,
                totalOrders: row.dailyOrders,
                totalSales: row.dailySales,
                totalCustomers: row.totalCustomers,
                averageOrderValue: row.dailyAOV,
                pendingOrders: row.dailyPendingOrders,
                pendingSales: row.dailyPendingSales,
                cancelledOrders: row.dailyCancelledOrders,
                cancelledSales: row.dailyCancelledSales,
            });

            if (Object.keys(cleanedSourceData).length > 1) { // Add source data only if it's not empty
                acc[row.day].sources.push(cleanedSourceData);
            }

            return acc;
        }, {})) : [];

        // Extract the first result from each key that is not `dailyMetrics` or `dailyMetricsBySource`
        const result = {
            currency: businessSettings.currencycode,
            totalSales: metrics.totalSales[0] ? metrics.totalSales[0].totalSales : 0,
            totalOrders: metrics.totalOrders[0] ? metrics.totalOrders[0].totalOrders : 0,
            totalCustomers: metrics.totalCustomers[0] ? metrics.totalCustomers[0].totalCustomers : 0,
            averageOrderValue: metrics.averageOrderValue[0] ? metrics.averageOrderValue[0].aov : 0,
            pendingSales: metrics.pendingSales[0] ? metrics.pendingSales[0].pendingSales : 0,
            pendingOrders: metrics.pendingOrders[0] ? metrics.pendingOrders[0].pendingOrders : 0,
            cancelledSales: metrics.cancelledSales[0] ? metrics.cancelledSales[0].cancelledSales : 0,
            cancelledOrders: metrics.cancelledOrders[0] ? metrics.cancelledOrders[0].cancelledOrders : 0,
            dailyMetrics: dailyMetrics,
            dailyMetricsBySource: dailyMetricsBySource,
            heatMap: heatMap
        };

        return {
            statusCode: 200,
            headers,
            body: JSON.stringify(result),
        };
    } catch (error) {
        console.error(error);
        return {
            headers,
            statusCode: 500,
            body: JSON.stringify({ error: err.message }),
        };
    }
};
