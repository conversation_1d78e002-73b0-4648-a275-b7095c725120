const db = require('../config/database');

/**
 * Get Business Details
 * @param {number} businessId - Business ID
 * @return {Promise<object>} - Business details
 */
module.exports.getIntegrationDetails = async (businessId, integrationType, serviceId = '', singleRecord) => {
    try {
        const specificID = (serviceId) ? " AND eatout_integration.t_i_id = ?" : ""
        const sql = "select eatout_integration.* , integrations.name, integrations.id as integration_id from thirdparty_integrations integrations join thirdparty_integration_eatouts eatout_integration  on eatout_integration.t_i_id = integrations.id where integrations.type = ? AND eatout_integration.rid = ?" + specificID;
        let result = await executeQuery(sql, [integrationType, businessId, serviceId]);

        result = (singleRecord) ? result[0] : result;
        return result;
    } catch (error) {
        throw error;
    }
};

// Function to execute a database query
function executeQuery(sqlQuery, mappedFields) {
    return new Promise((resolve, reject) => {
        db.query(sqlQuery, mappedFields, (error, results, fields) => {
            if (error) {
                reject(error);
            } else {
                resolve(results);
            }
        });
    });
}
