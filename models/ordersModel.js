const db = require('../config/database');

/**
 * Add Order Details
 * @param {number} orderDetails - Business ID
 * @return {Promise<object>} - Business details
 */
module.exports.addOrder = async (orderDetails) => {
    try {
        const sql = "INSERT INTO orders SET ?";
        let result = await executeQuery(sql, [orderDetails]);
        return result;
    } catch (error) {
        throw error;
    }
};

/**
 * Update Order Details
 * @param {number} orderDetails - Business ID
 * @return {Promise<object>} - Business details
 */
module.exports.updateOrder = async (businessId, recieptNo, orderDetails) => {
    try {
        const sql = "UPDATE orders SET ? WHERE r_id = ? AND receipt_no = ?";
        let result = await executeQuery(sql, [orderDetails, businessId, recieptNo]);
        return result;
    } catch (error) {
        throw error;
    }
};

module.exports.addPosNotifcation = async (orderDetails) => {

    try {
        const sql = "INSERT INTO pos_order_notification SET ? ";
        let result = await executeQuery(sql, [orderDetails]);
        return result;
    } catch (error) {
        throw error;
    }
};

module.exports.updatePosNotifcation = async (orderId, orderDetails) => {

    try {
        const sql = "UPDATE pos_order_notification SET ? WHERE order_id = ?";
        let result = await executeQuery(sql, [orderDetails, orderId]);
        return result;
    } catch (error) {
        throw error;
    }
};

module.exports.checkPosNotifcationEntry = async (orderId) => {

    try {
        const sql = "SELECT * FROM pos_order_notification where order_id = ? ";
        let queryResult = await executeQuery(sql, [orderId]);

        let result = {
            count: queryResult.length,
            result: queryResult
        }
        return result;
    } catch (error) {
        throw error;
    }
};

/**
 * Get Business Details
 * @param {number} orderDetails - Business ID
 * @return {Promise<object>} - Business details
 */
module.exports.addOrderDetails = async (orderDetailsArray) => {
    try {
        const results = [];
        const sql = "INSERT INTO orderdetails SET ?";
        for (const orderDetails of orderDetailsArray) {
            let result = await executeQuery(sql, [orderDetails]);
            results.push(result);
        }
        return results;
    } catch (error) {
        throw error;
    }
};

/**
 * Update Order Details
 * @param {number} orderDetails - Business ID
 * @return {Promise<object>} - Business details
 */
module.exports.removeOldOrderDetails = async (orderId) => {
    try {
        const sql = "UPDATE orderdetails SET orderid = -orderid  WHERE orderid = ? ";
        let result = await executeQuery(sql, [orderId]);
        return result;
    } catch (error) {
        throw error;
    }
};

module.exports.getOrder = async (businessId, orderId) => {
    try {
        const sql = "SELECT * FROM orders WHERE r_id = ? AND orderid = ?";
        let result = await executeQuery(sql, [businessId, orderId]);
        return result[0];
    } catch (error) {
        throw error;
    }
};

module.exports.getBulkOrder = async (businessId, orderRequest) => {
    try {
        const sql = "select * from orders  where r_id = ?  and  (DATE(convert_tz(date,@@session.time_zone, ?) )>= ? and DATE(convert_tz(date,@@session.time_zone,?) )<= ? ) order by orderid desc  LIMIT 0, 100";
        let queryResult = await executeQuery(sql, [businessId, orderRequest.time_zone, orderRequest.from_date, orderRequest.time_zone, orderRequest.to_date, orderRequest.offset, orderRequest.limit]);
        
        let result = {
            count: queryResult.length,
            result: queryResult
        }
        return result;
    } catch (error) {
        throw error;
    }
};

module.exports.getOrderByRecieptNo = async (businessId, recieptNo) => {
    try {
        const sql = "SELECT * FROM orders WHERE r_id = ? AND receipt_no = ?";
        let queryResult = await executeQuery(sql, [businessId, recieptNo]);

        let result = {
            count: queryResult.length,
            result: queryResult
        }

        return result;
    } catch (error) {
        throw error;
    }
};

module.exports.getOrderDetails = async (businessId, orderId) => {
    try {
        const sql = "SELECT * FROM orderdetails WHERE orderid = ?";
        let result = await executeQuery(sql, [orderId]);
        return result;
    } catch (error) {
        throw error;
    }
};

module.exports.getOrderTransactionStatus = async (orderId) => {
    try {
        const sql = "SELECT * FROM transaction_status_timing WHERE transaction_id = ?";
        let result = await executeQuery(sql, [orderId]);

        const lastTransaction = result[result.length - 1];
        return lastTransaction;
    } catch (error) {
        throw error;
    }
};

module.exports.getTransactionStatus = async () => {
    try {

        const sql = "SELECT * FROM transaction_status";
        let result = await executeQuery(sql, []);
        return result;
    } catch (error) {
        throw error;
    }
};

/**
 * Get Business Details
 * @param {number} orderDetails - Business ID
 * @return {Promise<object>} - Business details
 */
module.exports.addTransactionTimeline = async (orderDetailsArray, orderStatus = "Pending", statusId = "1") => {
    try {
        const transactionStatus = {
            transaction_id: orderDetailsArray.order_id,
            status: orderStatus,
            type: "order",
            date: new Date(),
            status_update_time: "order",
            transaction_status_id: statusId,
        }
        const sql = "INSERT INTO transaction_status_timing SET ?";
        let result = await executeQuery(sql, [transactionStatus]);
        return result;
    } catch (error) {
        throw error;
    }
};

// Function to execute a database query
function executeQuery(sqlQuery, mappedFields) {
    return new Promise((resolve, reject) => {
        db.query(sqlQuery, mappedFields, (error, results, fields) => {
            if (error) {
                reject(error);
                console.log(error)
            } else {
                resolve(results);
            }
        });
    });
}
